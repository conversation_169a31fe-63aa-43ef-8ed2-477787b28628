// Portfolio Animation System
document.addEventListener('DOMContentLoaded', function() {
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                // Add specific animation class based on data attribute
                const animationType = entry.target.dataset.animation;
                if (animationType) {
                    entry.target.classList.add(animationType);
                }
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Portfolio filter animation
    const filterButtons = document.querySelectorAll('.portfolio-filter li');
    const portfolioItems = document.querySelectorAll('.portfolio-grid > div');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            const filterValue = this.getAttribute('data-filter');
            
            portfolioItems.forEach(item => {
                if (filterValue === '*' || item.classList.contains(filterValue.substring(1))) {
                    item.style.display = 'block';
                    item.style.animation = 'fadeInUp 0.6s ease forwards';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Typing animation for banner text
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }

    // Initialize typing animation for main heading
    const mainHeading = document.querySelector('.banner_content h1');
    if (mainHeading) {
        const originalText = mainHeading.textContent;
        setTimeout(() => {
            typeWriter(mainHeading, originalText, 150);
        }, 1000);
    }

    // Counter animation for experience years
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        }
        updateCounter();
    }

    // Animate experience counter when visible
    const experienceCounter = document.querySelector('.lage');
    if (experienceCounter) {
        const counterObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(experienceCounter, 2, 2000);
                    counterObserver.unobserve(entry.target);
                }
            });
        });
        counterObserver.observe(experienceCounter);
    }

    // Parallax effect for banner
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const bannerImg = document.querySelector('.home_right_img img');
        if (bannerImg) {
            bannerImg.style.transform = `translateY(${scrolled * 0.3}px)`;
        }
    });

    // Add hover effects to skill logos
    document.querySelectorAll('.skill-logo').forEach(logo => {
        logo.addEventListener('mouseenter', function() {
            this.style.animation = 'pulse 0.6s ease';
        });
        
        logo.addEventListener('animationend', function() {
            this.style.animation = '';
        });
    });

    // Progressive image loading with fade-in effect
    function loadImage(img) {
        return new Promise((resolve, reject) => {
            const imageLoader = new Image();
            imageLoader.onload = () => resolve(imageLoader);
            imageLoader.onerror = reject;
            imageLoader.src = img.src;
        });
    }

    // Apply fade-in effect to images as they load
    document.querySelectorAll('img').forEach(img => {
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.5s ease';
        
        loadImage(img).then(() => {
            img.style.opacity = '1';
        }).catch(() => {
            // If image fails to load, still show placeholder
            img.style.opacity = '0.3';
        });
    });

    // Add scroll progress indicator
    function updateScrollProgress() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        let progressBar = document.querySelector('.scroll-progress');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'scroll-progress';
            progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: ${scrollPercent}%;
                height: 3px;
                background: linear-gradient(90deg, #4458dc, #854fee);
                z-index: 9999;
                transition: width 0.1s ease;
            `;
            document.body.appendChild(progressBar);
        } else {
            progressBar.style.width = scrollPercent + '%';
        }
    }

    window.addEventListener('scroll', updateScrollProgress);

    // Add staggered animation to skill items
    function staggerAnimation(elements, animationClass, delay = 100) {
        elements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add(animationClass);
            }, index * delay);
        });
    }

    // Apply staggered animations when skills section is visible
    const skillsSection = document.querySelector('#skills');
    if (skillsSection) {
        const skillsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const skillLogos = entry.target.querySelectorAll('.skill-logo');
                    staggerAnimation(skillLogos, 'bounce-in', 150);
                    skillsObserver.unobserve(entry.target);
                }
            });
        });
        skillsObserver.observe(skillsSection);
    }

    // Add floating animation to profile image
    const profileImg = document.querySelector('.home_right_img img');
    if (profileImg) {
        profileImg.classList.add('float-animation');
    }

    // Add glow effect to buttons on hover
    document.querySelectorAll('.primary_btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.classList.add('glow-animation');
        });
        
        btn.addEventListener('mouseleave', function() {
            this.classList.remove('glow-animation');
        });
    });

    console.log('Portfolio animations initialized successfully!');
});
